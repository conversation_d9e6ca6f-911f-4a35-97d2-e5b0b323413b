# 📦 نظام إدارة المنتجات - Invoice SaaS

## نظرة عامة

تم إضافة نظام إدارة المنتجات الكامل إلى منصة Invoice SaaS، والذي يوفر إدارة شاملة للمنتجات مع ميزات متقدمة للاستيراد والتصدير والبحث والتصفية.

## 🚀 المميزات الرئيسية

### 1. إدارة المنتجات الأساسية
- ✅ إضافة منتجات جديدة مع جميع التفاصيل
- ✅ تعديل المنتجات الموجودة
- ✅ حذف المنتجات مع تأكيد الأمان
- ✅ تفعيل/إلغاء تفعيل المنتجات
- ✅ نظام رموز المنتجات (SKU) الفريدة

### 2. البحث والتصفية المتقدم
- 🔍 البحث في اسم المنتج، الوصف، ورمز المنتج
- 📂 التصفية حسب الفئة
- 🔄 التصفية حسب الحالة (نشط/غير نشط)
- 📄 التنقل بين الصفحات مع عرض محدود
- 📊 عرض عدد النتائج والإحصائيات

### 3. استيراد ملفات Excel المحسن
- 📥 رفع ملفات Excel (.xlsx, .xls) و CSV مع دعم الترميز العربي الكامل
- 👁️ معاينة البيانات قبل الاستيراد مع عرض الأخطاء التفصيلية
- ✅ التحقق المتقدم من صحة البيانات تلقائياً مع رسائل واضحة
- 📋 تقرير مفصل عن عملية الاستيراد (نجح/فشل/تجاهل)
- 📄 نموذج Excel محسن قابل للتحميل مع بيانات تجريبية وتعليمات
- 📖 دليل شامل لعملية الاستيراد مع الأمثلة المرئية
- 🔍 التحقق من تكرار البيانات في الملف وقاعدة البيانات
- 🛠️ مكتبة مخصصة للتعامل مع ملفات Excel بدون اعتماديات خارجية

### 4. الإحصائيات والتقارير
- 📈 إجمالي المنتجات
- ✅ عدد المنتجات النشطة
- ⏸️ عدد المنتجات غير النشطة
- 💰 إجمالي قيمة المخزون
- 📦 إجمالي الكمية المتاحة

## 🗄️ هيكل قاعدة البيانات

### جدول المنتجات (products)

```sql
CREATE TABLE products (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    price DECIMAL(10,2) NOT NULL DEFAULT 0.00,
    quantity INT NOT NULL DEFAULT 0,
    category VARCHAR(100),
    sku VARCHAR(100),
    status ENUM('active', 'inactive') DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    UNIQUE KEY unique_user_sku (user_id, sku),
    INDEX idx_user_id (user_id),
    INDEX idx_category (category),
    INDEX idx_status (status),
    INDEX idx_sku (sku)
);
```

### الحقول والوصف

| الحقل | النوع | الوصف | مطلوب |
|-------|-------|--------|--------|
| `id` | INT | المعرف الفريد للمنتج | ✅ |
| `user_id` | INT | معرف المستخدم المالك | ✅ |
| `name` | VARCHAR(255) | اسم المنتج | ✅ |
| `description` | TEXT | وصف المنتج | ❌ |
| `price` | DECIMAL(10,2) | سعر المنتج | ✅ |
| `quantity` | INT | الكمية المتاحة | ✅ |
| `category` | VARCHAR(100) | فئة المنتج | ❌ |
| `sku` | VARCHAR(100) | رمز المنتج الفريد | ❌ |
| `status` | ENUM | حالة المنتج (active/inactive) | ✅ |
| `created_at` | TIMESTAMP | تاريخ الإنشاء | ✅ |
| `updated_at` | TIMESTAMP | تاريخ آخر تحديث | ✅ |

## 📁 الملفات المضافة

### الملفات الرئيسية
- `products.php` - صفحة عرض وإدارة المنتجات
- `add-product.php` - صفحة إضافة منتج جديد
- `edit-product.php` - صفحة تعديل المنتج
- `import-products.php` - صفحة استيراد ملفات Excel المحسنة
- `download-excel-template.php` - تحميل نموذج Excel محسن
- `excel-import-guide.php` - دليل شامل لاستيراد Excel
- `includes/SimpleExcelReader.php` - مكتبة مخصصة لقراءة ملفات Excel

### التحديثات على الملفات الموجودة
- `includes/header.php` - إضافة رابط المنتجات في القائمة
- `dashboard.php` - إضافة إحصائيات المنتجات
- `assets/css/style.css` - إضافة أنماط جديدة للمنتجات

## 🎨 واجهة المستخدم

### 1. صفحة المنتجات الرئيسية (`products.php`)
- **بطاقات الإحصائيات**: عرض سريع للأرقام المهمة
- **أدوات البحث والتصفية**: بحث متقدم وتصفية حسب الفئة والحالة
- **جدول المنتجات**: عرض منظم مع أزرار الإجراءات
- **التنقل بين الصفحات**: للتعامل مع عدد كبير من المنتجات

### 2. نموذج إضافة المنتج (`add-product.php`)
- **نموذج شامل**: جميع الحقول المطلوبة والاختيارية
- **التحقق من البيانات**: فحص صحة البيانات قبل الحفظ
- **اقتراحات الفئات**: عرض الفئات الموجودة للاختيار
- **إنشاء رمز تلقائي**: اقتراح رمز منتج تلقائياً

### 3. نموذج التعديل (`edit-product.php`)
- **تحميل البيانات الحالية**: عرض البيانات الموجودة
- **معلومات إضافية**: تاريخ الإنشاء والتحديث
- **حفظ التغييرات**: تحديث البيانات مع التحقق

### 4. استيراد Excel (`import-products.php`)
- **رفع الملف**: دعم ملفات .xls و .xlsx
- **معاينة البيانات**: عرض البيانات قبل الاستيراد
- **التحقق من الأخطاء**: فحص البيانات وعرض الأخطاء
- **تقرير الاستيراد**: نتائج مفصلة لعملية الاستيراد

## 📊 لوحة التحكم المحدثة

### إحصائيات المنتجات الجديدة
- **إجمالي المنتجات**: العدد الكلي للمنتجات
- **المنتجات النشطة**: المنتجات المتاحة للبيع
- **إجمالي المخزون**: مجموع الكميات
- **قيمة المخزون**: القيمة الإجمالية للمنتجات

### الإجراءات السريعة
- إضافة منتج جديد
- استيراد من Excel
- عرض جميع المنتجات
- عرض المنتجات غير النشطة

## 📥 استيراد ملفات Excel المحسن

### تنسيق الملف المطلوب

#### الأعمدة المطلوبة:
- `name` - اسم المنتج (نص، حتى 255 حرف)
- `price` - السعر (رقم موجب، يدعم الفواصل العشرية)
- `quantity` - الكمية (رقم صحيح موجب)

#### الأعمدة الاختيارية:
- `description` - وصف المنتج (نص طويل)
- `category` - فئة المنتج (نص، حتى 100 حرف)
- `sku` - رمز المنتج (نص فريد، حتى 100 حرف)
- `status` - الحالة (active/inactive/نشط/غير نشط)

### مثال على ملف Excel:

| name | description | price | quantity | category | sku | status |
|------|-------------|-------|----------|----------|-----|--------|
| لابتوب Dell Inspiron | لابتوب عالي الأداء مع معالج Intel Core i7 | 2500.00 | 10 | إلكترونيات | DELL-001 | active |
| هاتف Samsung Galaxy | هاتف ذكي بكاميرا عالية الدقة | 3200.00 | 25 | إلكترونيات | SAM-001 | active |
| كتاب تعلم البرمجة | دليل شامل لتعلم البرمجة | 150.00 | 50 | كتب | BOOK-001 | نشط |

**أو بصيغة CSV:**
```csv
name,description,price,quantity,category,sku,status
لابتوب Dell Inspiron,لابتوب عالي الأداء مع معالج Intel Core i7,2500.00,10,إلكترونيات,DELL-001,active
هاتف Samsung Galaxy,هاتف ذكي بكاميرا عالية الدقة,3200.00,25,إلكترونيات,SAM-001,active
كتاب تعلم البرمجة,دليل شامل لتعلم البرمجة,150.00,50,كتب,BOOK-001,نشط
```

### خطوات الاستيراد المحسنة:
1. **تحميل النموذج**: احصل على نموذج CSV مع بيانات تجريبية وتعليمات مفصلة
2. **قراءة الدليل**: راجع دليل الاستيراد الشامل مع الأمثلة
3. **تحضير البيانات**: املأ النموذج ببياناتك مع الحفاظ على التنسيق
4. **حفظ بترميز UTF-8**: تأكد من حفظ الملف بترميز UTF-8 لدعم العربية
5. **رفع ومعاينة**: ارفع الملف وراجع المعاينة مع التحقق من الأخطاء
6. **تأكيد الاستيراد**: أكد استيراد المنتجات الصحيحة فقط

### التحسينات الجديدة:
- ✅ **التحقق المتقدم**: فحص شامل للبيانات مع رسائل خطأ واضحة
- ✅ **دعم العربية الكامل**: ترميز UTF-8 مع BOM لدعم مثالي للنصوص العربية
- ✅ **منع التكرار**: فحص تكرار رموز المنتجات في الملف وقاعدة البيانات
- ✅ **التحقق من الطول**: فحص طول النصوص لتجنب أخطاء قاعدة البيانات
- ✅ **معاينة مرئية**: عرض مثال على تنسيق الملف في صفحة الاستيراد
- ✅ **دليل شامل**: صفحة مخصصة مع تعليمات مفصلة وأسئلة شائعة
- ✅ **مكتبة مخصصة**: SimpleExcelReader للتعامل مع ملفات Excel بدون اعتماديات
- ✅ **دعم متدرج**: يعمل مع أو بدون مكتبة PhpSpreadsheet
- ✅ **صفحة اختبار**: للتحقق من حالة دعم Excel في النظام

## 🔒 الأمان والصلاحيات

### التحقق من الهوية
- جميع الصفحات محمية بنظام تسجيل الدخول
- التحقق من صلاحية المستخدم لكل عملية

### حماية البيانات
- ربط المنتجات بالمستخدم المالك فقط
- منع الوصول للمنتجات الخاصة بمستخدمين آخرين
- التحقق من صحة البيانات قبل الحفظ

### التحقق من صحة البيانات
- فحص الحقول المطلوبة
- التأكد من صحة الأرقام والأسعار
- منع تكرار رموز المنتجات
- تنظيف البيانات من المحتوى الضار

## 🎯 الاستخدام

### إضافة منتج جديد:
1. انتقل إلى "المنتجات" من القائمة الرئيسية
2. اضغط على "إضافة منتج جديد"
3. املأ البيانات المطلوبة
4. احفظ المنتج

### البحث عن منتج:
1. في صفحة المنتجات، استخدم مربع البحث
2. اختر الفئة أو الحالة للتصفية
3. اضغط "بحث"

### استيراد منتجات من Excel:
1. اضغط "استيراد Excel" في صفحة المنتجات
2. حمل نموذج Excel أو استخدم ملفك
3. ارفع الملف وراجع المعاينة
4. أكد الاستيراد

## 🔧 التخصيص والتطوير

### إضافة حقول جديدة:
1. تعديل جدول قاعدة البيانات
2. تحديث نماذج الإدخال
3. تعديل صفحات العرض
4. تحديث نظام الاستيراد

### تخصيص التصميم:
- تعديل ملف `assets/css/style.css`
- إضافة ألوان وأنماط جديدة
- تخصيص تخطيط الصفحات

## 📈 الإحصائيات والتقارير

### المتوفر حالياً:
- إجمالي المنتجات
- المنتجات النشطة/غير النشطة
- قيمة المخزون الإجمالية
- إجمالي الكميات

### يمكن إضافتها مستقبلاً:
- تقارير المبيعات حسب المنتج
- تحليل الأداء
- تنبيهات نفاد المخزون
- تقارير الربحية

## 🚀 التحديثات المستقبلية

### مميزات مقترحة:
- [ ] نظام الباركود
- [ ] صور المنتجات
- [ ] إدارة المتغيرات (الألوان، الأحجام)
- [ ] نظام التنبيهات للمخزون المنخفض
- [ ] تكامل مع أنظمة المحاسبة
- [ ] تصدير التقارير PDF/Excel
- [ ] نظام الخصومات والعروض
- [ ] إدارة الموردين

## 📞 الدعم والمساعدة

للحصول على المساعدة أو الإبلاغ عن مشاكل:
- راجع هذا الدليل أولاً
- تحقق من رسائل الخطأ في الصفحات
- تأكد من صحة البيانات المدخلة
- تحقق من صلاحيات المستخدم

---

## 📝 ملاحظات التطوير

- تم تطوير النظام باستخدام PHP و MySQL
- يدعم اللغة العربية بالكامل
- متوافق مع Bootstrap 5
- يستخدم مكتبة PhpSpreadsheet لملفات Excel
- مصمم ليكون متجاوب مع جميع الأجهزة

**تاريخ الإنشاء**: 2024  
**الإصدار**: 1.0  
**المطور**: نظام Invoice SaaS
