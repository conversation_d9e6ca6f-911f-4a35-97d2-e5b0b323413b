# دليل قوالب الفواتير

## نظرة عامة
يحتوي النظام على مجموعة متنوعة من قوالب الفواتير المصممة لتلبية احتياجات مختلف أنواع الأعمال.

## القوالب المتاحة

### 1. القالب الكلاسيكي (Classic Template)
- **الوصف**: قالب بسيط ومهني للاستخدام العام
- **الفئة**: classic
- **المميزات**: تصميم نظيف وبسيط، سهل القراءة

### 2. القالب الحديث (Modern Template)
- **الوصف**: قالب عصري بألوان جذابة
- **الفئة**: modern
- **المميزات**: تصميم حديث مع ألوان متدرجة

### 3. القالب المهني (Professional Template)
- **الوصف**: قالب احترافي للشركات
- **الفئة**: professional
- **المميزات**: تصميم رسمي ومناسب للشركات الكبيرة

### 4. القالب الحديث المتطور (Modern Advanced Template) - جديد!
- **الوصف**: قالب حديث ومتطور مع تصميم عصري وألوان جذابة مستوحى من المدن الحديثة
- **الفئة**: modern
- **المميزات**:
  - تصميم مستوحى من أفق المدن الحديثة
  - ألوان متدرجة باللون الأزرق السماوي (#00bcd4)
  - شعار ديناميكي مع رسوم بيانية
  - تخطيط احترافي مع تقسيم واضح للمعلومات
  - تصميم متجاوب يعمل على جميع الأجهزة
  - معلومات دفع مفصلة
  - تذييل أنيق مع معلومات الاتصال

## المتغيرات المدعومة

جميع القوالب تدعم المتغيرات التالية:

### معلومات الفاتورة
- `{{invoice_number}}` - رقم الفاتورة
- `{{issue_date}}` - تاريخ الإصدار
- `{{due_date}}` - تاريخ الاستحقاق

### معلومات الشركة
- `{{company_name}}` - اسم الشركة
- `{{company_tagline}}` - شعار الشركة (للقالب المتطور)
- `{{company_address}}` - عنوان الشركة
- `{{company_city}}` - مدينة الشركة
- `{{company_country}}` - بلد الشركة
- `{{company_phone}}` - هاتف الشركة
- `{{company_email}}` - بريد الشركة الإلكتروني
- `{{company_website}}` - موقع الشركة الإلكتروني

### معلومات العميل
- `{{client_name}}` - اسم العميل
- `{{client_company}}` - شركة العميل
- `{{client_address}}` - عنوان العميل
- `{{client_city}}` - مدينة العميل
- `{{client_country}}` - بلد العميل
- `{{client_phone}}` - هاتف العميل
- `{{client_email}}` - بريد العميل الإلكتروني

### المبالغ المالية
- `{{subtotal}}` - المجموع الفرعي
- `{{tax_amount}}` - مبلغ الضريبة
- `{{total_amount}}` - المبلغ الإجمالي

### معلومات الدفع
- `{{bank_name}}` - اسم البنك
- `{{account_number}}` - رقم الحساب

### معلومات إضافية
- `{{user_name}}` - اسم المستخدم
- `{{notes}}` - ملاحظات
- `{{invoice_items}}` - عناصر الفاتورة (جدول HTML)

## كيفية إضافة قالب جديد

1. **إنشاء HTML للقالب**:
   ```html
   <div class="my-template">
       <h1>{{company_name}}</h1>
       <p>Invoice: {{invoice_number}}</p>
       <!-- باقي محتوى القالب -->
   </div>
   ```

2. **إنشاء CSS للقالب**:
   ```css
   .my-template {
       font-family: Arial, sans-serif;
       max-width: 800px;
       margin: 0 auto;
   }
   ```

3. **إدراج القالب في قاعدة البيانات**:
   ```sql
   INSERT INTO invoice_templates (name, description, category, html_template, css_styles, is_active)
   VALUES ('اسم القالب', 'وصف القالب', 'modern', 'HTML_CONTENT', 'CSS_CONTENT', 1);
   ```

## الملفات ذات الصلة

- `templates.php` - معرض القوالب
- `ajax/preview-template.php` - معاينة القوالب
- `ajax/live-preview.php` - المعاينة المباشرة
- `create-invoice.php` - إنشاء الفواتير باستخدام القوالب

## ملاحظات مهمة

1. **الاستجابة**: جميع القوالب مصممة لتكون متجاوبة مع جميع أحجام الشاشات
2. **الطباعة**: القوالب محسنة للطباعة مع إعدادات CSS خاصة
3. **الأمان**: جميع المتغيرات يتم تنظيفها قبل الإدراج لمنع XSS
4. **الأداء**: القوالب محسنة للأداء مع CSS مضغوط

## التحديثات الأخيرة

- **2024**: إضافة القالب الحديث المتطور مع تصميم مستوحى من أفق المدن
- تحسين نظام المعاينة المباشرة
- إضافة دعم لمتغيرات جديدة
- تحسين الاستجابة على الأجهزة المحمولة

---

للمزيد من المساعدة أو لإضافة قوالب جديدة، يرجى مراجعة الوثائق الفنية أو الاتصال بفريق التطوير.
