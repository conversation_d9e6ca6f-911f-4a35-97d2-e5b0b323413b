<?php
require_once '../config/config.php';

// التحقق من تسجيل الدخول
if (!isLoggedIn()) {
    echo '<div class="alert alert-warning">يجب تسجيل الدخول لرؤية المعاينة</div>';
    exit;
}

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo '<div class="alert alert-danger">طريقة الطلب غير صحيحة</div>';
    exit;
}

try {
    $pdo = getDBConnection();
    $user = getCurrentUser();
    
    // استلام البيانات
    $template_id = (int)($_POST['template_id'] ?? 1);
    $invoice_number = sanitize($_POST['invoice_number'] ?? '');
    $issue_date = $_POST['issue_date'] ?? date('Y-m-d');
    $due_date = $_POST['due_date'] ?? '';
    $client_id = (int)($_POST['client_id'] ?? 0);
    $currency = $_POST['currency'] ?? 'SAR';
    $tax_rate = (float)($_POST['tax_rate'] ?? 0);
    $discount_rate = (float)($_POST['discount_rate'] ?? 0);
    $notes = sanitize($_POST['notes'] ?? '');
    
    // جلب القالب
    $templateStmt = $pdo->prepare("SELECT * FROM invoice_templates WHERE id = ? AND is_active = 1");
    $templateStmt->execute([$template_id]);
    $template = $templateStmt->fetch();
    
    if (!$template) {
        echo '<div class="alert alert-danger">القالب غير موجود</div>';
        exit;
    }
    
    // جلب بيانات العميل
    $client_data = ['name' => '', 'email' => '', 'phone' => '', 'address' => ''];
    if ($client_id > 0) {
        $clientStmt = $pdo->prepare("SELECT * FROM clients WHERE id = ? AND user_id = ?");
        $clientStmt->execute([$client_id, $user['id']]);
        $client = $clientStmt->fetch();
        if ($client) {
            $client_data = $client;
        }
    }
    
    // معالجة العناصر
    $items = $_POST['items'] ?? [];
    $subtotal = 0;
    $items_html = '';
    
    if (!empty($items)) {
        foreach ($items as $item) {
            $description = sanitize($item['description'] ?? '');
            $quantity = (float)($item['quantity'] ?? 0);
            $unit_price = (float)($item['unit_price'] ?? 0);
            $total_price = $quantity * $unit_price;
            $subtotal += $total_price;
            
            if (!empty($description)) {
                $items_html .= '<tr>';
                $items_html .= '<td>' . htmlspecialchars($description) . '</td>';
                $items_html .= '<td>' . number_format($quantity, 2) . '</td>';
                $items_html .= '<td>' . number_format($unit_price, 2) . '</td>';
                $items_html .= '<td>' . number_format($total_price, 2) . '</td>';
                $items_html .= '</tr>';
            }
        }
    }
    
    // حساب المجاميع
    $tax_amount = $subtotal * ($tax_rate / 100);
    $discount_amount = $subtotal * ($discount_rate / 100);
    $total_amount = $subtotal + $tax_amount - $discount_amount;
    
    // رموز العملات
    $currency_symbols = [
        'SAR' => 'ريال',
        'USD' => '$',
        'EUR' => '€'
    ];
    $currency_symbol = $currency_symbols[$currency] ?? $currency;
    
    // بيانات الاستبدال
    $replacements = [
        'invoice_number' => $invoice_number ?: 'INV-XXXX',
        'issue_date' => $issue_date ? date('d/m/Y', strtotime($issue_date)) : date('d/m/Y'),
        'due_date' => $due_date ? date('d/m/Y', strtotime($due_date)) : '',
        'company_name' => ($user['company_name'] ?? '') ?: (($user['first_name'] ?? '') . ' ' . ($user['last_name'] ?? '')),
        'company_tagline' => 'التسويق العقاري',
        'company_address' => $user['address'] ?? 'عنوان الشركة',
        'company_city' => $user['city'] ?? 'المدينة',
        'company_country' => $user['country'] ?? 'البلد',
        'company_phone' => $user['phone'] ?? 'رقم الهاتف',
        'company_email' => $user['email'] ?? 'البريد الإلكتروني',
        'company_website' => 'www.company.com',
        'client_name' => $client_data['name'] ?? 'اسم العميل',
        'client_company' => $client_data['company'] ?? 'شركة العميل',
        'client_address' => $client_data['address'] ?? 'عنوان العميل',
        'client_city' => $client_data['city'] ?? 'مدينة العميل',
        'client_country' => $client_data['country'] ?? 'بلد العميل',
        'client_email' => $client_data['email'] ?? '',
        'client_phone' => $client_data['phone'] ?? '',
        'subtotal' => number_format($subtotal, 2) . ' ' . $currency_symbol,
        'tax_rate' => $tax_rate . '%',
        'tax_amount' => number_format($tax_amount, 2) . ' ' . $currency_symbol,
        'discount_rate' => $discount_rate . '%',
        'discount_amount' => number_format($discount_amount, 2) . ' ' . $currency_symbol,
        'total_amount' => number_format($total_amount, 2) . ' ' . $currency_symbol,
        'bank_name' => 'البنك الأهلي',
        'account_number' => '************',
        'user_name' => ($user['first_name'] ?? '') . ' ' . ($user['last_name'] ?? ''),
        'notes' => $notes,
        'invoice_items' => $items_html ?: '<tr><td colspan="4" class="text-center text-muted">لا توجد عناصر</td></tr>'
    ];
    
    // استبدال المتغيرات في القالب
    $html_content = $template['html_template'];
    foreach ($replacements as $key => $value) {
        $html_content = str_replace('{{' . $key . '}}', $value, $html_content);
    }
    
    // إضافة CSS
    echo '<style>' . $template['css_styles'] . '</style>';
    echo '<div class="live-preview-content">' . $html_content . '</div>';
    
} catch (Exception $e) {
    echo '<div class="alert alert-danger">حدث خطأ في تحديث المعاينة</div>';
}
?>

<style>
.live-preview-content {
    background: white;
    padding: 20px;
    border-radius: 10px;
    min-height: 400px;
}

.live-preview-content table {
    width: 100%;
    border-collapse: collapse;
    margin: 15px 0;
}

.live-preview-content th,
.live-preview-content td {
    padding: 8px 12px;
    text-align: right;
    border: 1px solid #ddd;
}

.live-preview-content th {
    background-color: #f8f9fa;
    font-weight: 600;
}

.live-preview-content .invoice-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
    padding-bottom: 15px;
    border-bottom: 2px solid #eee;
}

.live-preview-content .parties {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 30px;
    margin: 30px 0;
}

.live-preview-content .summary {
    text-align: left;
    margin-top: 20px;
}

.live-preview-content .summary-row {
    display: flex;
    justify-content: space-between;
    margin: 8px 0;
    padding: 5px 0;
}

.live-preview-content .total {
    font-weight: bold;
    font-size: 1.1em;
    border-top: 2px solid #007bff;
    padding-top: 10px;
    margin-top: 15px;
}

@media (max-width: 768px) {
    .live-preview-content .invoice-header {
        flex-direction: column;
        text-align: center;
    }
    
    .live-preview-content .parties {
        grid-template-columns: 1fr;
        gap: 20px;
    }
}
</style>
