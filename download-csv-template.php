<?php
require_once 'config/config.php';

// التحقق من تسجيل الدخول
if (!isLoggedIn()) {
    redirect(APP_URL . '/auth/login.php');
}

try {
    // إعداد العناوين
    $headers = ['name', 'description', 'price', 'quantity', 'category', 'sku', 'status'];
    
    // العناوين التوضيحية بالعربية
    $arabic_headers = [
        'اسم المنتج (مطلوب)',
        'وصف المنتج (اختياري)', 
        'السعر (مطلوب)',
        'الكمية (مطلوب)',
        'الفئة (اختياري)',
        'رمز المنتج (اختياري)',
        'الحالة (active/inactive)'
    ];
    
    // بيانات تجريبية متنوعة
    $sample_data = [
        ['لابتوب Dell Inspiron 15', 'لابتوب عالي الأداء مع معالج Intel Core i7 وذاكرة 16GB RAM', 2500.00, 10, 'إلكترونيات', 'DELL-INS-15-001', 'active'],
        ['هاتف Samsung Galaxy S23', 'هاتف ذكي بكاميرا عالية الدقة وشاشة AMOLED', 3200.00, 25, 'إلكترونيات', 'SAM-GAL-S23-001', 'active'],
        ['كتاب تعلم البرمجة', 'دليل شامل لتعلم البرمجة للمبتدئين', 150.00, 50, 'كتب', 'BOOK-PROG-001', 'active'],
        ['قميص قطني أزرق', 'قميص قطني عالي الجودة مقاس L', 85.00, 30, 'ملابس', 'SHIRT-BLUE-L-001', 'inactive'],
        ['ساعة ذكية Apple Watch', 'ساعة ذكية مع مراقب معدل ضربات القلب وGPS', 1800.00, 15, 'إلكترونيات', 'APPLE-WATCH-001', 'active'],
        ['كوب قهوة سيراميك', 'كوب قهوة أنيق من السيراميك الفاخر', 45.00, 100, 'أدوات منزلية', 'CUP-CERAMIC-001', 'active'],
        ['حقيبة ظهر رياضية', 'حقيبة ظهر مقاومة للماء للأنشطة الرياضية', 120.00, 40, 'رياضة', 'BAG-SPORT-001', 'active'],
        ['سماعات لاسلكية', 'سماعات بلوتوث عالية الجودة مع إلغاء الضوضاء', 450.00, 20, 'إلكترونيات', 'HEADPHONES-BT-001', 'active'],
        ['طاولة مكتب خشبية', 'طاولة مكتب من الخشب الطبيعي بتصميم عصري', 750.00, 8, 'أثاث', 'DESK-WOOD-001', 'active'],
        ['مصباح LED ذكي', 'مصباح LED قابل للتحكم عبر التطبيق مع ألوان متعددة', 95.00, 60, 'إلكترونيات', 'LED-SMART-001', 'active']
    ];
    
    // إنشاء محتوى CSV
    $csv_content = [];
    
    // إضافة العناوين الإنجليزية
    $csv_content[] = $headers;
    
    // إضافة العناوين العربية كتعليق
    $csv_content[] = $arabic_headers;
    
    // إضافة صف فارغ للفصل
    $csv_content[] = ['', '', '', '', '', '', ''];
    
    // إضافة البيانات التجريبية
    foreach ($sample_data as $data) {
        $csv_content[] = $data;
    }
    
    // إضافة ملاحظات مهمة
    $csv_content[] = ['', '', '', '', '', '', ''];
    $csv_content[] = ['=== ملاحظات مهمة ===', '', '', '', '', '', ''];
    $csv_content[] = ['1. الأعمدة المطلوبة: name, price, quantity', '', '', '', '', '', ''];
    $csv_content[] = ['2. الأعمدة الاختيارية: description, category, sku, status', '', '', '', '', '', ''];
    $csv_content[] = ['3. قيم الحالة المقبولة: active, inactive, نشط, غير نشط', '', '', '', '', '', ''];
    $csv_content[] = ['4. السعر والكمية يجب أن تكون أرقام موجبة', '', '', '', '', '', ''];
    $csv_content[] = ['5. رمز المنتج (SKU) يجب أن يكون فريد لكل مستخدم', '', '', '', '', '', ''];
    $csv_content[] = ['6. احذف هذه الصفوف والملاحظات قبل رفع الملف', '', '', '', '', '', ''];
    $csv_content[] = ['7. تأكد من حفظ الملف بترميز UTF-8', '', '', '', '', '', ''];
    $csv_content[] = ['8. استخدم الفاصلة (,) كفاصل بين الأعمدة', '', '', '', '', '', ''];
    $csv_content[] = ['', '', '', '', '', '', ''];
    $csv_content[] = ['=== أمثلة على القيم المقبولة ===', '', '', '', '', '', ''];
    $csv_content[] = ['الحالة: active, inactive, نشط, غير نشط', '', '', '', '', '', ''];
    $csv_content[] = ['السعر: 100, 150.50, 2500.00', '', '', '', '', '', ''];
    $csv_content[] = ['الكمية: 1, 10, 100', '', '', '', '', '', ''];
    $csv_content[] = ['الفئة: إلكترونيات, كتب, ملابس, أثاث', '', '', '', '', '', ''];
    $csv_content[] = ['رمز المنتج: PROD-001, LAPTOP-DELL-001', '', '', '', '', '', ''];
    
    // إعداد اسم الملف
    $filename = 'نموذج_المنتجات_' . date('Y-m-d_H-i-s') . '.csv';
    
    // إعداد headers للتحميل
    header('Content-Type: text/csv; charset=utf-8');
    header('Content-Disposition: attachment; filename="' . $filename . '"');
    header('Cache-Control: max-age=0');
    header('Cache-Control: max-age=1');
    header('Expires: Mon, 26 Jul 1997 05:00:00 GMT');
    header('Last-Modified: ' . gmdate('D, d M Y H:i:s') . ' GMT');
    header('Cache-Control: cache, must-revalidate');
    header('Pragma: public');
    
    // إضافة BOM للدعم الصحيح للعربية في Excel
    echo "\xEF\xBB\xBF";
    
    // إنشاء ملف CSV
    $output = fopen('php://output', 'w');
    
    foreach ($csv_content as $row) {
        fputcsv($output, $row);
    }
    
    fclose($output);
    
} catch (Exception $e) {
    // في حالة حدوث خطأ، إعادة توجيه مع رسالة خطأ
    setMessage('حدث خطأ في إنشاء النموذج: ' . $e->getMessage(), 'error');
    redirect('import-products.php');
}
?>
