<?php
require_once 'config/config.php';
require_once 'vendor/autoload.php';

use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;
use PhpOffice\PhpSpreadsheet\Style\Alignment;
use PhpOffice\PhpSpreadsheet\Style\Fill;
use PhpOffice\PhpSpreadsheet\Style\Border;

// التحقق من تسجيل الدخول
if (!isLoggedIn()) {
    redirect(APP_URL . '/auth/login.php');
}

try {
    // إنشاء ملف Excel جديد
    $spreadsheet = new Spreadsheet();
    $sheet = $spreadsheet->getActiveSheet();
    
    // تعيين اسم الورقة
    $sheet->setTitle('نموذج المنتجات');
    
    // العناوين
    $headers = [
        'A1' => 'name',
        'B1' => 'description', 
        'C1' => 'price',
        'D1' => 'quantity',
        'E1' => 'category',
        'F1' => 'sku',
        'G1' => 'status'
    ];
    
    // العناوين بالعربية للصف الثاني
    $arabic_headers = [
        'A2' => 'اسم المنتج (مطلوب)',
        'B2' => 'وصف المنتج (اختياري)',
        'C2' => 'السعر (مطلوب)',
        'D2' => 'الكمية (مطلوب)',
        'E2' => 'الفئة (اختياري)',
        'F2' => 'رمز المنتج (اختياري)',
        'G2' => 'الحالة (active/inactive)'
    ];
    
    // إضافة العناوين الإنجليزية
    foreach ($headers as $cell => $value) {
        $sheet->setCellValue($cell, $value);
    }
    
    // إضافة العناوين العربية
    foreach ($arabic_headers as $cell => $value) {
        $sheet->setCellValue($cell, $value);
    }
    
    // بيانات تجريبية
    $sample_data = [
        ['لابتوب Dell Inspiron', 'لابتوب عالي الأداء مع معالج Intel Core i7', 2500.00, 10, 'إلكترونيات', 'DELL-001', 'active'],
        ['هاتف Samsung Galaxy', 'هاتف ذكي بكاميرا عالية الدقة', 3200.00, 25, 'إلكترونيات', 'SAM-001', 'active'],
        ['كتاب تعلم البرمجة', 'دليل شامل لتعلم البرمجة للمبتدئين', 150.00, 50, 'كتب', 'BOOK-001', 'active'],
        ['قميص قطني', 'قميص قطني عالي الجودة', 85.00, 30, 'ملابس', 'SHIRT-001', 'inactive'],
        ['ساعة ذكية', 'ساعة ذكية مع مراقب معدل ضربات القلب', 1800.00, 15, 'إلكترونيات', 'WATCH-001', 'active']
    ];
    
    // إضافة البيانات التجريبية
    $row = 3;
    foreach ($sample_data as $data) {
        $sheet->setCellValue('A' . $row, $data[0]);
        $sheet->setCellValue('B' . $row, $data[1]);
        $sheet->setCellValue('C' . $row, $data[2]);
        $sheet->setCellValue('D' . $row, $data[3]);
        $sheet->setCellValue('E' . $row, $data[4]);
        $sheet->setCellValue('F' . $row, $data[5]);
        $sheet->setCellValue('G' . $row, $data[6]);
        $row++;
    }
    
    // تنسيق العناوين
    $headerStyle = [
        'font' => [
            'bold' => true,
            'color' => ['rgb' => 'FFFFFF']
        ],
        'fill' => [
            'fillType' => Fill::FILL_SOLID,
            'startColor' => ['rgb' => '4472C4']
        ],
        'alignment' => [
            'horizontal' => Alignment::HORIZONTAL_CENTER,
            'vertical' => Alignment::VERTICAL_CENTER
        ],
        'borders' => [
            'allBorders' => [
                'borderStyle' => Border::BORDER_THIN,
                'color' => ['rgb' => '000000']
            ]
        ]
    ];
    
    // تطبيق التنسيق على العناوين
    $sheet->getStyle('A1:G1')->applyFromArray($headerStyle);
    
    // تنسيق العناوين العربية
    $arabicHeaderStyle = [
        'font' => [
            'bold' => true,
            'color' => ['rgb' => 'FFFFFF']
        ],
        'fill' => [
            'fillType' => Fill::FILL_SOLID,
            'startColor' => ['rgb' => '70AD47']
        ],
        'alignment' => [
            'horizontal' => Alignment::HORIZONTAL_CENTER,
            'vertical' => Alignment::VERTICAL_CENTER
        ],
        'borders' => [
            'allBorders' => [
                'borderStyle' => Border::BORDER_THIN,
                'color' => ['rgb' => '000000']
            ]
        ]
    ];
    
    $sheet->getStyle('A2:G2')->applyFromArray($arabicHeaderStyle);
    
    // تنسيق البيانات التجريبية
    $dataStyle = [
        'borders' => [
            'allBorders' => [
                'borderStyle' => Border::BORDER_THIN,
                'color' => ['rgb' => 'CCCCCC']
            ]
        ],
        'alignment' => [
            'vertical' => Alignment::VERTICAL_CENTER
        ]
    ];
    
    $sheet->getStyle('A3:G7')->applyFromArray($dataStyle);
    
    // تعيين عرض الأعمدة
    $sheet->getColumnDimension('A')->setWidth(25); // اسم المنتج
    $sheet->getColumnDimension('B')->setWidth(40); // الوصف
    $sheet->getColumnDimension('C')->setWidth(12); // السعر
    $sheet->getColumnDimension('D')->setWidth(12); // الكمية
    $sheet->getColumnDimension('E')->setWidth(15); // الفئة
    $sheet->getColumnDimension('F')->setWidth(15); // رمز المنتج
    $sheet->getColumnDimension('G')->setWidth(15); // الحالة
    
    // تعيين ارتفاع الصفوف
    $sheet->getRowDimension(1)->setRowHeight(25);
    $sheet->getRowDimension(2)->setRowHeight(25);
    
    // إضافة ملاحظات
    $sheet->setCellValue('A9', 'ملاحظات مهمة:');
    $sheet->setCellValue('A10', '1. الأعمدة المطلوبة: name, price, quantity');
    $sheet->setCellValue('A11', '2. الأعمدة الاختيارية: description, category, sku, status');
    $sheet->setCellValue('A12', '3. قيم الحالة المقبولة: active, inactive, نشط, غير نشط');
    $sheet->setCellValue('A13', '4. السعر والكمية يجب أن تكون أرقام موجبة');
    $sheet->setCellValue('A14', '5. رمز المنتج (SKU) يجب أن يكون فريد');
    $sheet->setCellValue('A15', '6. احذف هذه الصفوف قبل رفع الملف');
    
    // تنسيق الملاحظات
    $notesStyle = [
        'font' => [
            'bold' => true,
            'color' => ['rgb' => 'D9534F']
        ]
    ];
    
    $sheet->getStyle('A9')->applyFromArray($notesStyle);
    $sheet->getStyle('A10:A15')->getFont()->setSize(10);
    
    // تجميد الصفوف العلوية
    $sheet->freezePane('A3');
    
    // إعداد الاستجابة لتحميل الملف
    $filename = 'نموذج_المنتجات_' . date('Y-m-d_H-i-s') . '.xlsx';
    
    header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
    header('Content-Disposition: attachment;filename="' . $filename . '"');
    header('Cache-Control: max-age=0');
    header('Cache-Control: max-age=1');
    header('Expires: Mon, 26 Jul 1997 05:00:00 GMT');
    header('Last-Modified: ' . gmdate('D, d M Y H:i:s') . ' GMT');
    header('Cache-Control: cache, must-revalidate');
    header('Pragma: public');
    
    $writer = new Xlsx($spreadsheet);
    $writer->save('php://output');
    
    // تنظيف الذاكرة
    $spreadsheet->disconnectWorksheets();
    unset($spreadsheet);
    
} catch (Exception $e) {
    // في حالة حدوث خطأ، إعادة توجيه مع رسالة خطأ
    setMessage('حدث خطأ في إنشاء النموذج: ' . $e->getMessage(), 'error');
    redirect('import-products.php');
}
?>
