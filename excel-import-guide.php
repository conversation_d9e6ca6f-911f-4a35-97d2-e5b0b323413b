<?php
require_once 'config/config.php';

// التحقق من تسجيل الدخول
if (!isLoggedIn()) {
    redirect(APP_URL . '/auth/login.php');
}

$page_title = 'دليل استيراد ملفات Excel';
include 'includes/header.php';
?>

<div class="container py-4">
    <!-- العنوان -->
    <div class="row mb-4">
        <div class="col-md-8">
            <h1 class="h3 mb-3">
                <i class="fas fa-book text-primary me-2"></i>
                دليل استيراد ملفات Excel
            </h1>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="dashboard.php">لوحة التحكم</a></li>
                    <li class="breadcrumb-item"><a href="products.php">المنتجات</a></li>
                    <li class="breadcrumb-item active">دليل الاستيراد</li>
                </ol>
            </nav>
        </div>
        <div class="col-md-4 text-end">
            <a href="import-products.php" class="btn btn-success">
                <i class="fas fa-upload me-2"></i>استيراد الآن
            </a>
            <a href="download-excel-template.php" class="btn btn-info">
                <i class="fas fa-download me-2"></i>تحميل النموذج
            </a>
        </div>
    </div>

    <div class="row">
        <div class="col-lg-8">
            <!-- الخطوات -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-list-ol me-2"></i>خطوات الاستيراد
                    </h5>
                </div>
                <div class="card-body">
                    <div class="step-item">
                        <div class="step-number">1</div>
                        <div class="step-content">
                            <h6>تحميل النموذج</h6>
                            <p>احصل على نموذج Excel الجاهز مع البيانات التجريبية والتعليمات المفصلة.</p>
                            <a href="download-excel-template.php" class="btn btn-sm btn-outline-info">
                                <i class="fas fa-download me-1"></i>تحميل نموذج Excel
                            </a>
                        </div>
                    </div>
                    
                    <div class="step-item">
                        <div class="step-number">2</div>
                        <div class="step-content">
                            <h6>تحضير البيانات</h6>
                            <p>املأ النموذج ببيانات منتجاتك مع الحفاظ على تنسيق الأعمدة.</p>
                            <ul class="small">
                                <li>احتفظ بالصف الأول (أسماء الأعمدة الإنجليزية)</li>
                                <li>احذف البيانات التجريبية والملاحظات</li>
                                <li>تأكد من صحة البيانات وعدم وجود خلايا فارغة في الأعمدة المطلوبة</li>
                            </ul>
                        </div>
                    </div>
                    
                    <div class="step-item">
                        <div class="step-number">3</div>
                        <div class="step-content">
                            <h6>حفظ الملف</h6>
                            <p>احفظ الملف بصيغة Excel (.xlsx) أو CSV مع ترميز UTF-8.</p>
                            <div class="alert alert-info small">
                                <strong>خيارات الحفظ:</strong><br>
                                • Excel (.xlsx) - الأفضل للنصوص العربية<br>
                                • CSV (UTF-8) - متوافق مع جميع البرامج
                            </div>
                        </div>
                    </div>
                    
                    <div class="step-item">
                        <div class="step-number">4</div>
                        <div class="step-content">
                            <h6>رفع ومعاينة</h6>
                            <p>ارفع الملف في صفحة الاستيراد وراجع المعاينة مع التحقق من الأخطاء.</p>
                            <a href="import-products.php" class="btn btn-sm btn-outline-success">
                                <i class="fas fa-upload me-1"></i>رفع الملف
                            </a>
                        </div>
                    </div>
                    
                    <div class="step-item">
                        <div class="step-number">5</div>
                        <div class="step-content">
                            <h6>تأكيد الاستيراد</h6>
                            <p>راجع البيانات وأكد الاستيراد للمنتجات الصحيحة فقط.</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- تنسيق الملف -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-table me-2"></i>تنسيق الملف المطلوب
                    </h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-bordered">
                            <thead class="table-primary">
                                <tr>
                                    <th>العمود</th>
                                    <th>الاسم الإنجليزي</th>
                                    <th>مطلوب؟</th>
                                    <th>الوصف</th>
                                    <th>مثال</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>A</td>
                                    <td><code>name</code></td>
                                    <td><span class="badge bg-danger">مطلوب</span></td>
                                    <td>اسم المنتج</td>
                                    <td>لابتوب Dell Inspiron</td>
                                </tr>
                                <tr>
                                    <td>B</td>
                                    <td><code>description</code></td>
                                    <td><span class="badge bg-secondary">اختياري</span></td>
                                    <td>وصف المنتج</td>
                                    <td>لابتوب عالي الأداء</td>
                                </tr>
                                <tr>
                                    <td>C</td>
                                    <td><code>price</code></td>
                                    <td><span class="badge bg-danger">مطلوب</span></td>
                                    <td>سعر المنتج</td>
                                    <td>2500.00</td>
                                </tr>
                                <tr>
                                    <td>D</td>
                                    <td><code>quantity</code></td>
                                    <td><span class="badge bg-danger">مطلوب</span></td>
                                    <td>الكمية المتاحة</td>
                                    <td>10</td>
                                </tr>
                                <tr>
                                    <td>E</td>
                                    <td><code>category</code></td>
                                    <td><span class="badge bg-secondary">اختياري</span></td>
                                    <td>فئة المنتج</td>
                                    <td>إلكترونيات</td>
                                </tr>
                                <tr>
                                    <td>F</td>
                                    <td><code>sku</code></td>
                                    <td><span class="badge bg-secondary">اختياري</span></td>
                                    <td>رمز المنتج الفريد</td>
                                    <td>DELL-001</td>
                                </tr>
                                <tr>
                                    <td>G</td>
                                    <td><code>status</code></td>
                                    <td><span class="badge bg-secondary">اختياري</span></td>
                                    <td>حالة المنتج</td>
                                    <td>active</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- مثال على Excel -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-file-excel me-2"></i>مثال على ملف Excel
                    </h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-bordered table-sm">
                            <thead class="table-success">
                                <tr>
                                    <th>name</th>
                                    <th>description</th>
                                    <th>price</th>
                                    <th>quantity</th>
                                    <th>category</th>
                                    <th>sku</th>
                                    <th>status</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr class="table-light">
                                    <td><small>اسم المنتج (مطلوب)</small></td>
                                    <td><small>وصف المنتج (اختياري)</small></td>
                                    <td><small>السعر (مطلوب)</small></td>
                                    <td><small>الكمية (مطلوب)</small></td>
                                    <td><small>الفئة (اختياري)</small></td>
                                    <td><small>رمز المنتج (اختياري)</small></td>
                                    <td><small>الحالة</small></td>
                                </tr>
                                <tr>
                                    <td>لابتوب Dell</td>
                                    <td>لابتوب عالي الأداء</td>
                                    <td>2500.00</td>
                                    <td>10</td>
                                    <td>إلكترونيات</td>
                                    <td>DELL-001</td>
                                    <td>active</td>
                                </tr>
                                <tr>
                                    <td>هاتف Samsung</td>
                                    <td>هاتف ذكي متطور</td>
                                    <td>3200.00</td>
                                    <td>25</td>
                                    <td>إلكترونيات</td>
                                    <td>SAM-001</td>
                                    <td>active</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <small class="text-muted">
                        <i class="fas fa-info-circle me-1"></i>
                        الصف الأول يحتوي على أسماء الأعمدة الإنجليزية، والصف الثاني يحتوي على التوضيحات العربية (يمكن حذفه)
                    </small>
                </div>
            </div>

            <!-- القيم المقبولة -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-check-circle me-2"></i>القيم المقبولة
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6>حالة المنتج (status):</h6>
                            <ul class="list-unstyled">
                                <li><code>active</code> - منتج نشط</li>
                                <li><code>inactive</code> - منتج غير نشط</li>
                                <li><code>نشط</code> - منتج نشط (عربي)</li>
                                <li><code>غير نشط</code> - منتج غير نشط (عربي)</li>
                                <li><em>فارغ</em> - يعتبر نشط افتراضياً</li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <h6>تنسيق الأرقام:</h6>
                            <ul class="list-unstyled">
                                <li><strong>السعر:</strong> أرقام موجبة (100, 150.50, 2500.00)</li>
                                <li><strong>الكمية:</strong> أرقام صحيحة موجبة (1, 10, 100)</li>
                                <li><strong>رمز المنتج:</strong> نص فريد (PROD-001, LAPTOP-DELL-001)</li>
                                <li><strong>الفئة:</strong> نص (إلكترونيات, كتب, ملابس)</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- الشريط الجانبي -->
        <div class="col-lg-4">
            <div class="card mb-4">
                <div class="card-header">
                    <h6 class="card-title mb-0">
                        <i class="fas fa-lightbulb me-2"></i>نصائح مهمة
                    </h6>
                </div>
                <div class="card-body">
                    <div class="alert alert-success">
                        <h6><i class="fas fa-thumbs-up me-2"></i>أفضل الممارسات:</h6>
                        <ul class="mb-0 small">
                            <li>استخدم النموذج المتوفر</li>
                            <li>تحقق من البيانات قبل الرفع</li>
                            <li>استخدم أرقام منتجات فريدة</li>
                            <li>احفظ نسخة احتياطية</li>
                            <li>احفظ بصيغة Excel للحصول على أفضل دعم للعربية</li>
                        </ul>
                    </div>
                    
                    <div class="alert alert-warning">
                        <h6><i class="fas fa-exclamation-triangle me-2"></i>تجنب:</h6>
                        <ul class="mb-0 small">
                            <li>ترك الحقول المطلوبة فارغة</li>
                            <li>استخدام أرقام سالبة</li>
                            <li>تكرار رموز المنتجات</li>
                            <li>استخدام رموز خاصة في الأسماء</li>
                            <li>تغيير أسماء الأعمدة في الصف الأول</li>
                        </ul>
                    </div>
                    
                    <div class="alert alert-info">
                        <h6><i class="fas fa-file-excel me-2"></i>أنواع الملفات المدعومة:</h6>
                        <ul class="mb-0 small">
                            <li><strong>.xlsx</strong> - Excel 2007+ (الأفضل)</li>
                            <li><strong>.xls</strong> - Excel القديم</li>
                            <li><strong>.csv</strong> - ملف نصي مفصول بفواصل</li>
                        </ul>
                    </div>
                </div>
            </div>

            <div class="card">
                <div class="card-header">
                    <h6 class="card-title mb-0">
                        <i class="fas fa-question-circle me-2"></i>الأسئلة الشائعة
                    </h6>
                </div>
                <div class="card-body">
                    <div class="accordion" id="faqAccordion">
                        <div class="accordion-item">
                            <h2 class="accordion-header">
                                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#faq1">
                                    ما هو الحد الأقصى لعدد المنتجات؟
                                </button>
                            </h2>
                            <div id="faq1" class="accordion-collapse collapse" data-bs-parent="#faqAccordion">
                                <div class="accordion-body small">
                                    يمكنك استيراد حتى 1000 منتج في الملف الواحد. حجم الملف الأقصى 5 ميجابايت.
                                </div>
                            </div>
                        </div>
                        
                        <div class="accordion-item">
                            <h2 class="accordion-header">
                                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#faq2">
                                    ماذا يحدث للمنتجات المكررة؟
                                </button>
                            </h2>
                            <div id="faq2" class="accordion-collapse collapse" data-bs-parent="#faqAccordion">
                                <div class="accordion-body small">
                                    سيتم تجاهل المنتجات التي لها نفس رمز المنتج (SKU) أو المكررة في نفس الملف.
                                </div>
                            </div>
                        </div>
                        
                        <div class="accordion-item">
                            <h2 class="accordion-header">
                                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#faq3">
                                    هل يمكنني تعديل المنتجات بعد الاستيراد؟
                                </button>
                            </h2>
                            <div id="faq3" class="accordion-collapse collapse" data-bs-parent="#faqAccordion">
                                <div class="accordion-body small">
                                    نعم، يمكنك تعديل أي منتج من صفحة المنتجات أو حذفه أو تغيير حالته.
                                </div>
                            </div>
                        </div>
                        
                        <div class="accordion-item">
                            <h2 class="accordion-header">
                                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#faq4">
                                    أي صيغة أفضل: Excel أم CSV؟
                                </button>
                            </h2>
                            <div id="faq4" class="accordion-collapse collapse" data-bs-parent="#faqAccordion">
                                <div class="accordion-body small">
                                    Excel (.xlsx) أفضل للنصوص العربية، بينما CSV أكثر توافقاً مع البرامج المختلفة.
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.step-item {
    display: flex;
    margin-bottom: 20px;
    padding-bottom: 20px;
    border-bottom: 1px solid #eee;
}

.step-item:last-child {
    border-bottom: none;
    margin-bottom: 0;
    padding-bottom: 0;
}

.step-number {
    width: 40px;
    height: 40px;
    background: #007bff;
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    margin-left: 15px;
    flex-shrink: 0;
}

.step-content {
    flex: 1;
}

.step-content h6 {
    margin-bottom: 8px;
    color: #333;
}

.step-content p {
    margin-bottom: 10px;
    color: #666;
}
</style>

<?php include 'includes/footer.php'; ?>
