<?php
require_once 'config/config.php';
require_once 'includes/SafeFileReader.php';

// محاولة تحميل PhpSpreadsheet إذا كانت متوفرة
if (file_exists('vendor/autoload.php')) {
    require_once 'vendor/autoload.php';
}

// التحقق من تسجيل الدخول
if (!isLoggedIn()) {
    redirect(APP_URL . '/auth/login.php');
}

$user = getCurrentUser();
$user_id = $user['id'];

$success = '';
$error = '';
$preview_data = [];
$import_results = [];

// معالجة تحميل الملف
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    try {
        $pdo = getDBConnection();
        
        if (isset($_POST['action']) && $_POST['action'] == 'preview') {
            // معاينة الملف
            if (!isset($_FILES['excel_file']) || $_FILES['excel_file']['error'] !== UPLOAD_ERR_OK) {
                throw new Exception('يرجى اختيار ملف Excel صحيح');
            }

            $file = $_FILES['excel_file'];
            $allowed_types = [
                'application/vnd.ms-excel',
                'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                'text/csv',
                'application/csv',
                'text/plain',
                'application/octet-stream' // للملفات التي لا يتم التعرف على نوعها
            ];
            $file_extension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
            $allowed_extensions = ['xlsx', 'xls', 'csv'];

            // التحقق من الامتداد أولاً (أكثر موثوقية من MIME type)
            if (!in_array($file_extension, $allowed_extensions)) {
                throw new Exception('نوع الملف غير مدعوم. يرجى رفع ملف Excel (.xlsx, .xls) أو CSV (.csv)');
            }

            // التحقق من MIME type كفحص إضافي (ليس إجباري)
            if (!in_array($file['type'], $allowed_types)) {
                // تحذير فقط، لا نوقف العملية
                error_log("تحذير: نوع MIME غير متوقع: " . $file['type'] . " للملف: " . $file['name']);
            }

            if ($file['size'] > 5 * 1024 * 1024) { // 5MB
                throw new Exception('حجم الملف كبير جداً. الحد الأقصى 5 ميجابايت');
            }

            // قراءة الملف باستخدام SafeFileReader
            try {
                $reader = new SafeFileReader($file['tmp_name'], $file['name']);
                $reader->validateFile();
                $rows = $reader->readFile();
            } catch (Exception $e) {
                throw new Exception('فشل في قراءة الملف: ' . $e->getMessage());
            }
            
            if (empty($rows)) {
                throw new Exception('الملف فارغ أو لا يحتوي على بيانات');
            }
            
            // التحقق من وجود العناوين المطلوبة
            $headers = array_map('trim', $rows[0]);
            $required_headers = ['name', 'price', 'quantity'];
            $optional_headers = ['description', 'category', 'sku', 'status'];
            $all_headers = array_merge($required_headers, $optional_headers);

            // التحقق من العناوين المطلوبة
            $missing_headers = [];
            foreach ($required_headers as $header) {
                if (!in_array($header, $headers)) {
                    $missing_headers[] = $header;
                }
            }

            if (!empty($missing_headers)) {
                throw new Exception('العناوين المطلوبة مفقودة: ' . implode(', ', $missing_headers));
            }

            // التحقق من وجود عناوين غير معروفة
            $unknown_headers = [];
            foreach ($headers as $header) {
                if (!in_array($header, $all_headers) && !empty($header)) {
                    $unknown_headers[] = $header;
                }
            }

            if (!empty($unknown_headers)) {
                $error = 'عناوين غير معروفة في الملف: ' . implode(', ', $unknown_headers) . '. ';
                $error .= 'العناوين المقبولة: ' . implode(', ', $all_headers);
                throw new Exception($error);
            }
            
            // معالجة البيانات
            $preview_data = [];
            $errors = [];
            
            for ($i = 1; $i < count($rows); $i++) {
                $row = $rows[$i];
                if (empty(array_filter($row))) continue; // تجاهل الصفوف الفارغة
                
                $product = [];
                $row_errors = [];
                
                foreach ($headers as $index => $header) {
                    $value = isset($row[$index]) ? trim($row[$index]) : '';
                    
                    switch ($header) {
                        case 'name':
                            if (empty($value)) {
                                $row_errors[] = 'اسم المنتج مطلوب';
                            }
                            $product['name'] = $value;
                            break;
                            
                        case 'price':
                            if (empty($value)) {
                                $row_errors[] = 'السعر مطلوب';
                                $product['price'] = 0;
                            } else {
                                $price = (float)str_replace(',', '', $value); // إزالة الفواصل
                                if ($price < 0) {
                                    $row_errors[] = 'السعر يجب أن يكون أكبر من أو يساوي صفر';
                                } elseif (!is_numeric($value) && !is_numeric(str_replace(',', '', $value))) {
                                    $row_errors[] = 'السعر يجب أن يكون رقم صحيح';
                                }
                                $product['price'] = $price;
                            }
                            break;

                        case 'quantity':
                            if (empty($value)) {
                                $row_errors[] = 'الكمية مطلوبة';
                                $product['quantity'] = 0;
                            } else {
                                $quantity = (int)$value;
                                if ($quantity < 0) {
                                    $row_errors[] = 'الكمية يجب أن تكون أكبر من أو يساوي صفر';
                                } elseif (!is_numeric($value)) {
                                    $row_errors[] = 'الكمية يجب أن تكون رقم صحيح';
                                }
                                $product['quantity'] = $quantity;
                            }
                            break;
                            
                        case 'description':
                            $product['description'] = $value;
                            break;
                            
                        case 'category':
                            $product['category'] = $value;
                            break;
                            
                        case 'sku':
                            $product['sku'] = $value;
                            break;
                            
                        case 'status':
                            $status = trim(strtolower($value));
                            $valid_statuses = ['active', 'inactive', 'نشط', 'غير نشط', ''];
                            if (!in_array($status, $valid_statuses)) {
                                $row_errors[] = 'الحالة يجب أن تكون: active, inactive, نشط، أو غير نشط';
                            }
                            // تحويل القيم العربية والافتراضية
                            if ($status == 'نشط' || $status == 'active' || empty($status)) {
                                $product['status'] = 'active';
                            } else {
                                $product['status'] = 'inactive';
                            }
                            break;
                    }
                }
                
                // التحقق من تكرار رمز المنتج في قاعدة البيانات
                if (!empty($product['sku'])) {
                    $stmt = $pdo->prepare("SELECT COUNT(*) FROM products WHERE user_id = ? AND sku = ?");
                    $stmt->execute([$user_id, $product['sku']]);
                    if ($stmt->fetchColumn() > 0) {
                        $row_errors[] = 'رمز المنتج موجود بالفعل في قاعدة البيانات';
                    }
                }

                // التحقق من تكرار رمز المنتج في نفس الملف
                static $used_skus = [];
                if (!empty($product['sku'])) {
                    if (in_array($product['sku'], $used_skus)) {
                        $row_errors[] = 'رمز المنتج مكرر في نفس الملف';
                    } else {
                        $used_skus[] = $product['sku'];
                    }
                }

                // التحقق من طول النصوص
                if (strlen($product['name']) > 255) {
                    $row_errors[] = 'اسم المنتج طويل جداً (الحد الأقصى 255 حرف)';
                }

                if (!empty($product['sku']) && strlen($product['sku']) > 100) {
                    $row_errors[] = 'رمز المنتج طويل جداً (الحد الأقصى 100 حرف)';
                }

                if (!empty($product['category']) && strlen($product['category']) > 100) {
                    $row_errors[] = 'اسم الفئة طويل جداً (الحد الأقصى 100 حرف)';
                }
                
                $product['row_number'] = $i + 1;
                $product['errors'] = $row_errors;
                $product['valid'] = empty($row_errors);
                
                $preview_data[] = $product;
            }
            
            if (empty($preview_data)) {
                throw new Exception('لا توجد بيانات صحيحة للاستيراد');
            }
            
            // حفظ البيانات في الجلسة للاستيراد لاحقاً
            $_SESSION['import_preview'] = $preview_data;
            
        } elseif (isset($_POST['action']) && $_POST['action'] == 'import') {
            // تنفيذ الاستيراد
            if (!isset($_SESSION['import_preview'])) {
                throw new Exception('لا توجد بيانات للاستيراد. يرجى معاينة الملف أولاً');
            }
            
            $preview_data = $_SESSION['import_preview'];
            $imported_count = 0;
            $skipped_count = 0;
            $import_results = [];
            
            $stmt = $pdo->prepare("
                INSERT INTO products (user_id, name, description, price, quantity, category, sku, status)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            ");
            
            foreach ($preview_data as $product) {
                if (!$product['valid']) {
                    $skipped_count++;
                    $import_results[] = [
                        'row' => $product['row_number'],
                        'name' => $product['name'],
                        'status' => 'skipped',
                        'message' => 'تم تجاهل الصف بسبب أخطاء: ' . implode(', ', $product['errors'])
                    ];
                    continue;
                }
                
                try {
                    $stmt->execute([
                        $user_id,
                        $product['name'],
                        $product['description'] ?? '',
                        $product['price'],
                        $product['quantity'],
                        $product['category'] ?? '',
                        $product['sku'] ?? '',
                        $product['status'] ?? 'active'
                    ]);
                    
                    $imported_count++;
                    $import_results[] = [
                        'row' => $product['row_number'],
                        'name' => $product['name'],
                        'status' => 'success',
                        'message' => 'تم الاستيراد بنجاح'
                    ];
                    
                } catch (Exception $e) {
                    $skipped_count++;
                    $import_results[] = [
                        'row' => $product['row_number'],
                        'name' => $product['name'],
                        'status' => 'error',
                        'message' => 'خطأ: ' . $e->getMessage()
                    ];
                }
            }
            
            unset($_SESSION['import_preview']);
            
            $success = "تم استيراد {$imported_count} منتج بنجاح. تم تجاهل {$skipped_count} منتج.";
            
            // إعادة توجيه إلى صفحة المنتجات بعد 5 ثوانٍ
            if ($imported_count > 0) {
                header("refresh:5;url=products.php");
            }
        }
        
    } catch (Exception $e) {
        $error = $e->getMessage();
        unset($_SESSION['import_preview']);
    }
}

$page_title = 'استيراد المنتجات من Excel';
include 'includes/header.php';
?>

<div class="container py-4">
    <!-- رسائل النجاح والخطأ -->
    <?php if ($success): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="fas fa-check-circle me-2"></i><?php echo $success; ?>
            <?php if (!empty($import_results) && count(array_filter($import_results, function($r) { return $r['status'] == 'success'; })) > 0): ?>
                <div class="mt-2">
                    <small>سيتم توجيهك إلى صفحة المنتجات خلال ثوانٍ...</small>
                </div>
            <?php endif; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>
    
    <?php if ($error): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-circle me-2"></i><?php echo $error; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <!-- العنوان -->
    <div class="row mb-4">
        <div class="col-md-8">
            <h1 class="h3 mb-3">
                <i class="fas fa-file-excel text-success me-2"></i>
                استيراد المنتجات من Excel
            </h1>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="dashboard.php">لوحة التحكم</a></li>
                    <li class="breadcrumb-item"><a href="products.php">المنتجات</a></li>
                    <li class="breadcrumb-item active">استيراد Excel</li>
                </ol>
            </nav>
        </div>
        <div class="col-md-4 text-end">
            <a href="products.php" class="btn btn-secondary">
                <i class="fas fa-arrow-right me-2"></i>العودة إلى المنتجات
            </a>
            <div class="btn-group">
                <a href="download-simple-excel-template.php" class="btn btn-info">
                    <i class="fas fa-download me-2"></i>تحميل نموذج Excel
                </a>
                <button type="button" class="btn btn-info dropdown-toggle dropdown-toggle-split" data-bs-toggle="dropdown">
                    <span class="visually-hidden">خيارات إضافية</span>
                </button>
                <ul class="dropdown-menu">
                    <li><a class="dropdown-item" href="download-simple-excel-template.php">
                        <i class="fas fa-file-excel me-2"></i>نموذج Excel (موصى به)
                    </a></li>
                    <li><a class="dropdown-item" href="download-excel-template.php">
                        <i class="fas fa-file-csv me-2"></i>نموذج CSV متوافق
                    </a></li>
                </ul>
            </div>
            <a href="excel-import-guide.php" class="btn btn-outline-primary">
                <i class="fas fa-book me-2"></i>دليل الاستيراد
            </a>
            <a href="debug-file-upload.php" class="btn btn-outline-warning">
                <i class="fas fa-bug me-2"></i>تشخيص المشاكل
            </a>
        </div>
    </div>

    <?php if (empty($preview_data) && empty($import_results)): ?>
        <!-- نموذج رفع الملف -->
        <div class="row">
            <div class="col-lg-8">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-upload me-2"></i>رفع ملف Excel
                        </h5>
                    </div>
                    <div class="card-body">
                        <form method="POST" enctype="multipart/form-data" id="uploadForm">
                            <input type="hidden" name="action" value="preview">

                            <div class="mb-4">
                                <label for="excel_file" class="form-label">اختر ملف Excel <span class="text-danger">*</span></label>
                                <input type="file" class="form-control" id="excel_file" name="excel_file"
                                       accept=".xlsx,.xls,.csv" required>
                                <div class="form-text">
                                    الملفات المدعومة: .xlsx, .xls, .csv | الحد الأقصى: 5 ميجابايت
                                </div>
                            </div>

                            <div class="alert alert-info">
                                <h6><i class="fas fa-info-circle me-2"></i>تنسيق الملف المطلوب:</h6>
                                <p class="mb-2">يجب أن يحتوي ملف Excel على الأعمدة التالية في الصف الأول:</p>
                                <div class="row">
                                    <div class="col-md-6">
                                        <strong>أعمدة مطلوبة:</strong>
                                        <ul class="mb-0">
                                            <li><code>name</code> - اسم المنتج</li>
                                            <li><code>price</code> - السعر</li>
                                            <li><code>quantity</code> - الكمية</li>
                                        </ul>
                                    </div>
                                    <div class="col-md-6">
                                        <strong>أعمدة اختيارية:</strong>
                                        <ul class="mb-0">
                                            <li><code>description</code> - الوصف</li>
                                            <li><code>category</code> - الفئة</li>
                                            <li><code>sku</code> - رمز المنتج</li>
                                            <li><code>status</code> - الحالة (active/inactive)</li>
                                        </ul>
                                    </div>
                                </div>
                            </div>

                            <!-- معاينة مرئية لتنسيق الملف -->
                            <div class="alert alert-light border">
                                <h6><i class="fas fa-eye me-2"></i>مثال على تنسيق الملف:</h6>
                                <div class="table-responsive">
                                    <table class="table table-sm table-bordered">
                                        <thead class="table-primary">
                                            <tr>
                                                <th>name</th>
                                                <th>description</th>
                                                <th>price</th>
                                                <th>quantity</th>
                                                <th>category</th>
                                                <th>sku</th>
                                                <th>status</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr class="table-success">
                                                <td>لابتوب Dell</td>
                                                <td>لابتوب عالي الأداء</td>
                                                <td>2500.00</td>
                                                <td>10</td>
                                                <td>إلكترونيات</td>
                                                <td>DELL-001</td>
                                                <td>active</td>
                                            </tr>
                                            <tr class="table-success">
                                                <td>هاتف Samsung</td>
                                                <td>هاتف ذكي متطور</td>
                                                <td>3200.00</td>
                                                <td>25</td>
                                                <td>إلكترونيات</td>
                                                <td>SAM-001</td>
                                                <td>active</td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                                <small class="text-muted">
                                    <i class="fas fa-info-circle me-1"></i>
                                    الصف الأول يحتوي على أسماء الأعمدة، والصفوف التالية تحتوي على البيانات
                                </small>
                            </div>

                            <div class="d-flex justify-content-between">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-eye me-2"></i>معاينة البيانات
                                </button>
                                <a href="download-simple-excel-template.php" class="btn btn-outline-info">
                                    <i class="fas fa-download me-2"></i>تحميل نموذج Excel
                                </a>
                            </div>
                        </form>
                    </div>
                </div>
            </div>

            <!-- الشريط الجانبي -->
            <div class="col-lg-4">
                <div class="card">
                    <div class="card-header">
                        <h6 class="card-title mb-0">
                            <i class="fas fa-lightbulb me-2"></i>نصائح مهمة
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-warning">
                            <h6><i class="fas fa-exclamation-triangle me-2"></i>قبل الاستيراد:</h6>
                            <ul class="mb-0 small">
                                <li>تأكد من صحة تنسيق البيانات</li>
                                <li>استخدم النموذج المتوفر لضمان التوافق</li>
                                <li>تحقق من عدم تكرار أرقام المنتجات (SKU)</li>
                                <li>تأكد من أن الأسعار والكميات أرقام صحيحة</li>
                                <li>احفظ الملف بصيغة Excel (.xlsx) أو CSV (UTF-8)</li>
                                <li>احفظ نسخة احتياطية من بياناتك</li>
                            </ul>
                        </div>

                        <div class="alert alert-info">
                            <h6><i class="fas fa-info-circle me-2"></i>ملاحظات:</h6>
                            <ul class="mb-0 small">
                                <li>سيتم تجاهل الصفوف التي تحتوي على أخطاء</li>
                                <li>يمكنك مراجعة البيانات قبل الاستيراد النهائي</li>
                                <li>المنتجات المكررة لن يتم استيرادها</li>
                                <li>الحد الأقصى 1000 منتج في الملف الواحد</li>
                                <li>يدعم ملفات Excel والترميز العربي بالكامل</li>
                            </ul>
                        </div>

                        <div class="alert alert-success">
                            <h6><i class="fas fa-check-circle me-2"></i>القيم المقبولة:</h6>
                            <div class="small">
                                <strong>الحالة:</strong> active, inactive, نشط, غير نشط<br>
                                <strong>السعر:</strong> أرقام موجبة (مثل: 100, 150.50)<br>
                                <strong>الكمية:</strong> أرقام صحيحة موجبة<br>
                                <strong>رمز المنتج:</strong> نص فريد (مثل: PROD-001)
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    <?php endif; ?>

    <?php if (!empty($preview_data)): ?>
        <!-- معاينة البيانات -->
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-eye me-2"></i>معاينة البيانات
                    <span class="badge bg-primary ms-2"><?php echo count($preview_data); ?> منتج</span>
                </h5>
            </div>
            <div class="card-body">
                <?php
                $valid_count = count(array_filter($preview_data, function($p) { return $p['valid']; }));
                $invalid_count = count($preview_data) - $valid_count;
                ?>

                <div class="row mb-3">
                    <div class="col-md-4">
                        <div class="alert alert-success">
                            <strong><?php echo $valid_count; ?></strong> منتج صحيح
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="alert alert-danger">
                            <strong><?php echo $invalid_count; ?></strong> منتج يحتوي على أخطاء
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="alert alert-info">
                            <strong><?php echo count($preview_data); ?></strong> إجمالي المنتجات
                        </div>
                    </div>
                </div>

                <?php if ($valid_count > 0): ?>
                    <div class="mb-3">
                        <form method="POST">
                            <input type="hidden" name="action" value="import">
                            <button type="submit" class="btn btn-success" onclick="return confirm('هل أنت متأكد من استيراد <?php echo $valid_count; ?> منتج؟')">
                                <i class="fas fa-check me-2"></i>استيراد المنتجات الصحيحة (<?php echo $valid_count; ?>)
                            </button>
                            <a href="import-products.php" class="btn btn-secondary ms-2">
                                <i class="fas fa-times me-2"></i>إلغاء
                            </a>
                        </form>
                    </div>
                <?php endif; ?>

                <div class="table-responsive">
                    <table class="table table-sm">
                        <thead class="table-dark">
                            <tr>
                                <th>الصف</th>
                                <th>اسم المنتج</th>
                                <th>السعر</th>
                                <th>الكمية</th>
                                <th>الفئة</th>
                                <th>رمز المنتج</th>
                                <th>الحالة</th>
                                <th>الحالة</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($preview_data as $product): ?>
                                <tr class="<?php echo $product['valid'] ? 'table-success' : 'table-danger'; ?>">
                                    <td><?php echo $product['row_number']; ?></td>
                                    <td><?php echo htmlspecialchars($product['name']); ?></td>
                                    <td><?php echo number_format($product['price'], 2); ?> ر.س</td>
                                    <td><?php echo number_format($product['quantity']); ?></td>
                                    <td><?php echo htmlspecialchars($product['category'] ?? '-'); ?></td>
                                    <td><?php echo htmlspecialchars($product['sku'] ?? '-'); ?></td>
                                    <td>
                                        <span class="badge <?php echo ($product['status'] ?? 'active') == 'active' ? 'bg-success' : 'bg-warning'; ?>">
                                            <?php echo ($product['status'] ?? 'active') == 'active' ? 'نشط' : 'غير نشط'; ?>
                                        </span>
                                    </td>
                                    <td>
                                        <?php if ($product['valid']): ?>
                                            <span class="badge bg-success">صحيح</span>
                                        <?php else: ?>
                                            <span class="badge bg-danger" title="<?php echo implode(', ', $product['errors']); ?>">خطأ</span>
                                            <br><small class="text-danger"><?php echo implode('<br>', $product['errors']); ?></small>
                                        <?php endif; ?>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    <?php endif; ?>

    <?php if (!empty($import_results)): ?>
        <!-- نتائج الاستيراد -->
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-list-check me-2"></i>نتائج الاستيراد
                </h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-sm">
                        <thead class="table-dark">
                            <tr>
                                <th>الصف</th>
                                <th>اسم المنتج</th>
                                <th>الحالة</th>
                                <th>الرسالة</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($import_results as $result): ?>
                                <tr class="<?php
                                    echo $result['status'] == 'success' ? 'table-success' :
                                        ($result['status'] == 'error' ? 'table-danger' : 'table-warning');
                                ?>">
                                    <td><?php echo $result['row']; ?></td>
                                    <td><?php echo htmlspecialchars($result['name']); ?></td>
                                    <td>
                                        <span class="badge <?php
                                            echo $result['status'] == 'success' ? 'bg-success' :
                                                ($result['status'] == 'error' ? 'bg-danger' : 'bg-warning');
                                        ?>">
                                            <?php
                                            echo $result['status'] == 'success' ? 'نجح' :
                                                ($result['status'] == 'error' ? 'فشل' : 'تم التجاهل');
                                            ?>
                                        </span>
                                    </td>
                                    <td><?php echo htmlspecialchars($result['message']); ?></td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>

                <div class="mt-3">
                    <a href="products.php" class="btn btn-primary">
                        <i class="fas fa-boxes me-2"></i>عرض المنتجات
                    </a>
                    <a href="import-products.php" class="btn btn-secondary">
                        <i class="fas fa-plus me-2"></i>استيراد ملف آخر
                    </a>
                </div>
            </div>
        </div>
    <?php endif; ?>
</div>

<script>
// التحقق من صحة الملف قبل الرفع
document.getElementById('uploadForm').addEventListener('submit', function(e) {
    const fileInput = document.getElementById('excel_file');
    const file = fileInput.files[0];

    if (!file) {
        alert('يرجى اختيار ملف Excel');
        e.preventDefault();
        return;
    }

    const fileName = file.name.toLowerCase();
    const allowedExtensions = ['.xlsx', '.xls', '.csv'];
    const isValidExtension = allowedExtensions.some(ext => fileName.endsWith(ext));

    if (!isValidExtension) {
        alert('نوع الملف غير مدعوم. يرجى رفع ملف Excel (.xlsx, .xls) أو CSV (.csv)');
        e.preventDefault();
        return;
    }

    if (file.size > 5 * 1024 * 1024) {
        alert('حجم الملف كبير جداً. الحد الأقصى 5 ميجابايت');
        e.preventDefault();
        return;
    }
});
</script>

<?php include 'includes/footer.php'; ?>
