<?php
require_once 'config/config.php';

// التحقق من تسجيل الدخول
if (!isLoggedIn()) {
    redirect(APP_URL . '/auth/login.php');
}

$user = getCurrentUser();
$user_id = $user['id'];

// معالجة حذف المنتج
$success = '';
$error = '';

if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['action'])) {
    try {
        $pdo = getDBConnection();
        
        if ($_POST['action'] == 'delete_product') {
            $product_id = (int)$_POST['product_id'];
            
            // التحقق من ملكية المنتج
            $stmt = $pdo->prepare("SELECT id FROM products WHERE id = ? AND user_id = ?");
            $stmt->execute([$product_id, $user_id]);
            
            if ($stmt->fetch()) {
                $stmt = $pdo->prepare("DELETE FROM products WHERE id = ? AND user_id = ?");
                $stmt->execute([$product_id, $user_id]);
                $success = 'تم حذف المنتج بنجاح';
            } else {
                $error = 'المنتج غير موجود أو ليس لديك صلاحية لحذفه';
            }
        } elseif ($_POST['action'] == 'toggle_status') {
            $product_id = (int)$_POST['product_id'];
            $new_status = $_POST['status'] == 'active' ? 'inactive' : 'active';
            
            $stmt = $pdo->prepare("UPDATE products SET status = ? WHERE id = ? AND user_id = ?");
            $stmt->execute([$new_status, $product_id, $user_id]);
            $success = 'تم تحديث حالة المنتج بنجاح';
        }
    } catch (Exception $e) {
        $error = 'حدث خطأ: ' . $e->getMessage();
    }
}

// معالجة البحث والتصفية
$search = $_GET['search'] ?? '';
$category_filter = $_GET['category'] ?? '';
$status_filter = $_GET['status'] ?? '';
$page = (int)($_GET['page'] ?? 1);
$per_page = 10;
$offset = ($page - 1) * $per_page;

try {
    $pdo = getDBConnection();
    
    // بناء استعلام البحث
    $where_conditions = ["user_id = ?"];
    $params = [$user_id];
    
    if (!empty($search)) {
        $where_conditions[] = "(name LIKE ? OR description LIKE ? OR sku LIKE ?)";
        $search_term = "%{$search}%";
        $params[] = $search_term;
        $params[] = $search_term;
        $params[] = $search_term;
    }
    
    if (!empty($category_filter)) {
        $where_conditions[] = "category = ?";
        $params[] = $category_filter;
    }
    
    if (!empty($status_filter)) {
        $where_conditions[] = "status = ?";
        $params[] = $status_filter;
    }
    
    $where_clause = implode(' AND ', $where_conditions);
    
    // عدد المنتجات الإجمالي
    $count_sql = "SELECT COUNT(*) FROM products WHERE {$where_clause}";
    $stmt = $pdo->prepare($count_sql);
    $stmt->execute($params);
    $total_products = $stmt->fetchColumn();
    $total_pages = ceil($total_products / $per_page);
    
    // جلب المنتجات
    $sql = "SELECT * FROM products WHERE {$where_clause} ORDER BY created_at DESC LIMIT {$per_page} OFFSET {$offset}";
    $stmt = $pdo->prepare($sql);
    $stmt->execute($params);
    $products = $stmt->fetchAll();
    
    // جلب الفئات للتصفية
    $stmt = $pdo->prepare("SELECT DISTINCT category FROM products WHERE user_id = ? AND category IS NOT NULL ORDER BY category");
    $stmt->execute([$user_id]);
    $categories = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    // إحصائيات
    $stmt = $pdo->prepare("SELECT COUNT(*) as total FROM products WHERE user_id = ?");
    $stmt->execute([$user_id]);
    $stats['total'] = $stmt->fetchColumn();
    
    $stmt = $pdo->prepare("SELECT COUNT(*) as active FROM products WHERE user_id = ? AND status = 'active'");
    $stmt->execute([$user_id]);
    $stats['active'] = $stmt->fetchColumn();
    
    $stmt = $pdo->prepare("SELECT COUNT(*) as inactive FROM products WHERE user_id = ? AND status = 'inactive'");
    $stmt->execute([$user_id]);
    $stats['inactive'] = $stmt->fetchColumn();
    
    $stmt = $pdo->prepare("SELECT SUM(quantity * price) as total_value FROM products WHERE user_id = ? AND status = 'active'");
    $stmt->execute([$user_id]);
    $stats['total_value'] = $stmt->fetchColumn() ?? 0;
    
} catch (Exception $e) {
    $error = 'حدث خطأ في جلب البيانات: ' . $e->getMessage();
    $products = [];
    $categories = [];
    $stats = ['total' => 0, 'active' => 0, 'inactive' => 0, 'total_value' => 0];
}

$page_title = 'إدارة المنتجات';
include 'includes/header.php';
?>

<div class="container-fluid py-4">
    <!-- رسائل النجاح والخطأ -->
    <?php if ($success): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="fas fa-check-circle me-2"></i><?php echo $success; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>
    
    <?php if ($error): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-circle me-2"></i><?php echo $error; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <!-- العنوان والإحصائيات -->
    <div class="row mb-4">
        <div class="col-md-8">
            <h1 class="h3 mb-3">
                <i class="fas fa-boxes text-primary me-2"></i>
                إدارة المنتجات
            </h1>
        </div>
        <div class="col-md-4 text-end">
            <a href="add-product.php" class="btn btn-primary">
                <i class="fas fa-plus me-2"></i>إضافة منتج جديد
            </a>
            <a href="import-products.php" class="btn btn-success">
                <i class="fas fa-file-csv me-2"></i>استيراد CSV
            </a>
        </div>
    </div>

    <!-- بطاقات الإحصائيات -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="card-title"><?php echo number_format($stats['total']); ?></h4>
                            <p class="card-text">إجمالي المنتجات</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-boxes fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="card-title"><?php echo number_format($stats['active']); ?></h4>
                            <p class="card-text">المنتجات النشطة</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-check-circle fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-warning text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="card-title"><?php echo number_format($stats['inactive']); ?></h4>
                            <p class="card-text">المنتجات غير النشطة</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-pause-circle fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-info text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="card-title"><?php echo number_format($stats['total_value'], 2); ?> ر.س</h4>
                            <p class="card-text">إجمالي القيمة</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-dollar-sign fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- أدوات البحث والتصفية -->
    <div class="card mb-4">
        <div class="card-body">
            <form method="GET" class="row g-3">
                <div class="col-md-4">
                    <label for="search" class="form-label">البحث</label>
                    <input type="text" class="form-control" id="search" name="search" 
                           value="<?php echo htmlspecialchars($search); ?>" 
                           placeholder="ابحث في اسم المنتج، الوصف، أو رمز المنتج">
                </div>
                <div class="col-md-3">
                    <label for="category" class="form-label">الفئة</label>
                    <select class="form-select" id="category" name="category">
                        <option value="">جميع الفئات</option>
                        <?php foreach ($categories as $category): ?>
                            <option value="<?php echo htmlspecialchars($category); ?>" 
                                    <?php echo $category_filter == $category ? 'selected' : ''; ?>>
                                <?php echo htmlspecialchars($category); ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>
                <div class="col-md-3">
                    <label for="status" class="form-label">الحالة</label>
                    <select class="form-select" id="status" name="status">
                        <option value="">جميع الحالات</option>
                        <option value="active" <?php echo $status_filter == 'active' ? 'selected' : ''; ?>>نشط</option>
                        <option value="inactive" <?php echo $status_filter == 'inactive' ? 'selected' : ''; ?>>غير نشط</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <label class="form-label">&nbsp;</label>
                    <div class="d-grid">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-search me-2"></i>بحث
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- جدول المنتجات -->
    <div class="card">
        <div class="card-header">
            <h5 class="card-title mb-0">
                <i class="fas fa-list me-2"></i>قائمة المنتجات
                <span class="badge bg-secondary ms-2"><?php echo number_format($total_products); ?> منتج</span>
            </h5>
        </div>
        <div class="card-body">
            <?php if (empty($products)): ?>
                <div class="text-center py-5">
                    <i class="fas fa-boxes fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">لا توجد منتجات</h5>
                    <p class="text-muted">ابدأ بإضافة منتجك الأول</p>
                    <a href="add-product.php" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>إضافة منتج جديد
                    </a>
                </div>
            <?php else: ?>
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead class="table-dark">
                            <tr>
                                <th>المنتج</th>
                                <th>الفئة</th>
                                <th>السعر</th>
                                <th>الكمية</th>
                                <th>الحالة</th>
                                <th>تاريخ الإضافة</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($products as $product): ?>
                                <tr>
                                    <td>
                                        <div>
                                            <strong><?php echo htmlspecialchars($product['name']); ?></strong>
                                            <?php if ($product['sku']): ?>
                                                <br><small class="text-muted">رمز المنتج: <?php echo htmlspecialchars($product['sku']); ?></small>
                                            <?php endif; ?>
                                            <?php if ($product['description']): ?>
                                                <br><small class="text-muted"><?php echo htmlspecialchars(substr($product['description'], 0, 100)); ?><?php echo strlen($product['description']) > 100 ? '...' : ''; ?></small>
                                            <?php endif; ?>
                                        </div>
                                    </td>
                                    <td>
                                        <?php if ($product['category']): ?>
                                            <span class="badge bg-secondary"><?php echo htmlspecialchars($product['category']); ?></span>
                                        <?php else: ?>
                                            <span class="text-muted">غير محدد</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <strong><?php echo number_format($product['price'], 2); ?> ر.س</strong>
                                    </td>
                                    <td>
                                        <span class="badge <?php echo $product['quantity'] > 0 ? 'bg-success' : 'bg-danger'; ?>">
                                            <?php echo number_format($product['quantity']); ?>
                                        </span>
                                    </td>
                                    <td>
                                        <form method="POST" style="display: inline;">
                                            <input type="hidden" name="action" value="toggle_status">
                                            <input type="hidden" name="product_id" value="<?php echo $product['id']; ?>">
                                            <input type="hidden" name="status" value="<?php echo $product['status']; ?>">
                                            <button type="submit" class="btn btn-sm <?php echo $product['status'] == 'active' ? 'btn-success' : 'btn-warning'; ?>"
                                                    onclick="return confirm('هل تريد تغيير حالة المنتج؟')">
                                                <i class="fas <?php echo $product['status'] == 'active' ? 'fa-check-circle' : 'fa-pause-circle'; ?>"></i>
                                                <?php echo $product['status'] == 'active' ? 'نشط' : 'غير نشط'; ?>
                                            </button>
                                        </form>
                                    </td>
                                    <td>
                                        <small class="text-muted">
                                            <?php echo date('Y-m-d H:i', strtotime($product['created_at'])); ?>
                                        </small>
                                    </td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <a href="edit-product.php?id=<?php echo $product['id']; ?>"
                                               class="btn btn-sm btn-outline-primary" title="تعديل">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <button type="button" class="btn btn-sm btn-outline-danger"
                                                    onclick="deleteProduct(<?php echo $product['id']; ?>, '<?php echo htmlspecialchars($product['name']); ?>')"
                                                    title="حذف">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>

                <!-- التنقل بين الصفحات -->
                <?php if ($total_pages > 1): ?>
                    <nav aria-label="تنقل الصفحات" class="mt-4">
                        <ul class="pagination justify-content-center">
                            <?php if ($page > 1): ?>
                                <li class="page-item">
                                    <a class="page-link" href="?page=<?php echo $page - 1; ?>&search=<?php echo urlencode($search); ?>&category=<?php echo urlencode($category_filter); ?>&status=<?php echo urlencode($status_filter); ?>">السابق</a>
                                </li>
                            <?php endif; ?>

                            <?php for ($i = max(1, $page - 2); $i <= min($total_pages, $page + 2); $i++): ?>
                                <li class="page-item <?php echo $i == $page ? 'active' : ''; ?>">
                                    <a class="page-link" href="?page=<?php echo $i; ?>&search=<?php echo urlencode($search); ?>&category=<?php echo urlencode($category_filter); ?>&status=<?php echo urlencode($status_filter); ?>"><?php echo $i; ?></a>
                                </li>
                            <?php endfor; ?>

                            <?php if ($page < $total_pages): ?>
                                <li class="page-item">
                                    <a class="page-link" href="?page=<?php echo $page + 1; ?>&search=<?php echo urlencode($search); ?>&category=<?php echo urlencode($category_filter); ?>&status=<?php echo urlencode($status_filter); ?>">التالي</a>
                                </li>
                            <?php endif; ?>
                        </ul>
                    </nav>
                <?php endif; ?>
            <?php endif; ?>
        </div>
    </div>
</div>

<!-- نموذج حذف المنتج -->
<form id="deleteForm" method="POST" style="display: none;">
    <input type="hidden" name="action" value="delete_product">
    <input type="hidden" name="product_id" id="deleteProductId">
</form>

<script>
function deleteProduct(productId, productName) {
    if (confirm('هل أنت متأكد من حذف المنتج "' + productName + '"؟\nهذا الإجراء لا يمكن التراجع عنه.')) {
        document.getElementById('deleteProductId').value = productId;
        document.getElementById('deleteForm').submit();
    }
}

// تحديث الصفحة كل 30 ثانية لعرض التحديثات
setTimeout(function() {
    location.reload();
}, 30000);
</script>

<?php include 'includes/footer.php'; ?>
